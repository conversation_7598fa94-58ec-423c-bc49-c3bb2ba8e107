name: conan-package

on:
  push:
    paths:
      - 'include/**'
      - 'src/**'
      - 'test_package/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - 'CMakeLists.txt'
      - '.github/workflows/conan-package.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - 'NP-*'
      - '[0-9].[0-9]*'
      - '[0-9].[0-9][0-9]*'
    tags:
      - '[0-9]+.[0-9]+.[0-9]*'
      - '[0-9]+.[0-9]+.[0-9]'
  pull_request:
    types: [opened, reopened, synchronize]
    paths:
      - 'include/**'
      - 'src/**'
      - 'test_package/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - 'CMakeLists.txt'
      - '.github/workflows/conan-package.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - 'NP-*'
      - '[0-9].[0-9]*'
      - '[0-9].[0-9][0-9]*'

jobs:
  conan-recipe-version:
    uses: ultimaker/cura-workflows/.github/workflows/conan-recipe-version.yml@main
    with:
      project_name: curaengine

  conan-package-export:
    needs: [ conan-recipe-version ]
    uses: ultimaker/cura-workflows/.github/workflows/conan-recipe-export.yml@main
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
      recipe_id_latest: ${{ needs.conan-recipe-version.outputs.recipe_id_latest }}
    secrets: inherit

  conan-package-create-macos:
    needs: [ conan-recipe-version, conan-package-export ]
    if: ${{ ((github.event_name == 'push' && (github.ref_name == 'main' || needs.conan-recipe-version.outputs.is_release_branch == 'true')) || github.event_name == 'pull_request') }}
    uses: ultimaker/cura-workflows/.github/workflows/conan-package-create-macos.yml@main
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
    secrets: inherit

  conan-package-create-windows:
    needs: [ conan-recipe-version, conan-package-export ]
    if: ${{ ((github.event_name == 'push' && (github.ref_name == 'main' || needs.conan-recipe-version.outputs.is_release_branch == 'true')) || github.event_name == 'pull_request') }}
    uses: ultimaker/cura-workflows/.github/workflows/conan-package-create-windows.yml@main
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
    secrets: inherit

  conan-package-create-linux:
    needs: [ conan-recipe-version, conan-package-export ]
    if: ${{ ((github.event_name == 'push' && (github.ref_name == 'main' || needs.conan-recipe-version.outputs.is_release_branch == 'true')) || github.event_name == 'pull_request') }}
    uses: ultimaker/cura-workflows/.github/workflows/conan-package-create-linux.yml@main
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
    secrets: inherit

  conan-package-create-wasm:
    needs: [ conan-recipe-version, conan-package-export ]
    if: ${{ (github.event_name == 'push' && (github.ref_name == 'main' || needs.conan-recipe-version.outputs.is_release_branch == 'true')) }}
    uses: ultimaker/cura-workflows/.github/workflows/conan-package-create-wasm.yml@main
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
    secrets: inherit
