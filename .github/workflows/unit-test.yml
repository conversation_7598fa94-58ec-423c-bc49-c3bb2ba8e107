name: unit-test

on:
  push:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'cura/**'
      - 'icons/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/requirements-runner.txt'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - '[0-9]+.[0-9]+'

  pull_request:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'cura/**'
      - 'icons/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/requirements-runner.txt'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - '[0-9]+.[0-9]+'

permissions:
  contents: read

env:
  CONAN_LOGIN_USERNAME: ${{ secrets.CONAN_USER }}
  CONAN_PASSWORD: ${{ secrets.CONAN_PASS }}

jobs:
  conan-recipe-version:
    uses: ultimaker/cura-workflows/.github/workflows/conan-recipe-version.yml@main
    with:
      project_name: cura

  testing:
    uses: ultimaker/cura-workflows/.github/workflows/unit-test.yml@main
    needs: [ conan-recipe-version ]
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
      conan_extra_args: '-g VirtualPythonEnv -o cura:devtools=True -c tools.build:skip_test=False --options "*:enable_sentry=False"'
      unit_test_cmd: 'pytest --junitxml=junit_cura.xml'
      unit_test_dir: 'tests'
      conan_generator_dir: './venv/bin'
    secrets: inherit