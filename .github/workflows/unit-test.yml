---
name: unit-test
# FIXME: This should be a reusable workflow

on:
  push:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'UM/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - '[0-9]+.[0-9]+'

  pull_request:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'UM/**'
      - 'icons/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - '[0-9]+.[0-9]+'

permissions:
  contents: read

env:
  CONAN_LOGIN_USERNAME: ${{ secrets.CONAN_USER }}
  CONAN_PASSWORD: ${{ secrets.CONAN_PASS }}

jobs:
  conan-recipe-version:
    uses: ultimaker/cura-workflows/.github/workflows/conan-recipe-version.yml@main
    with:
      project_name: uranium

  testing:
    uses: ultimaker/cura-workflows/.github/workflows/unit-test.yml@main
    needs: [ conan-recipe-version ]
    with:
      recipe_id_full: ${{ needs.conan-recipe-version.outputs.recipe_id_full }}
      conan_extra_args: '-g VirtualPythonEnv -o uranium:devtools=True -c tools.build:skip_test=False  --options "*:enable_sentry=False"'
      unit_test_cmd: 'pytest --junitxml=junit_uranium.xml'
      unit_test_dir: 'tests'
      conan_generator_dir: './venv/bin'
