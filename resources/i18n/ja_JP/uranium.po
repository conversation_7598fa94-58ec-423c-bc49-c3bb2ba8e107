msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-08 09:10+0200\n"
"PO-Revision-Date: 2024-10-30 02:17+0000\n"
"Last-Translator: h1data <<EMAIL>>\n"
"Language-Team: Japanese <https://github.com/h1data/Cura/wiki>\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgctxt "@info:version-upgrade"
msgid "A configuration from an older version of {0} was imported."
msgstr "過去のバージョン {0} のコンフィグレーションがインポートされました。"

msgctxt "@error:Required plugins not found"
msgid "A number of plugins are required, but could not be loaded: {plugins}"
msgstr "多数のプラグインが必要ですが、読み込めませんでした：{plugins}"

msgctxt "@option"
msgid "Abort"
msgstr "中止"

msgctxt "@item:inlistbox"
msgid "All Files (*)"
msgstr "全てのファイル (*)"

msgctxt "@item:inlistbox"
msgid "All Supported Types ({0})"
msgstr "サポートされたすべてのタイプ ({0})"

msgctxt "@option"
msgid "Apply"
msgstr "適用"

msgctxt "@label (%1 is object name)"
msgid "Are you sure you wish to remove %1? This cannot be undone!"
msgstr "%1を取り外しますか？この操作は取り消しできません。"

msgctxt "@info:status"
msgid "Auto scaled model to {0}% of original size"
msgstr "元のサイズの{0}%まで自動的にスケールされたモデル"

msgctxt "name"
msgid "Camera Tool"
msgstr "カメラツール"

msgctxt "@action:button"
msgid "Cancel"
msgstr "キャンセル"

msgctxt "@option"
msgid "Cancel"
msgstr "キャンセル"

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Cannot open files of the type of <filename>{0}</filename>"
msgstr "<filename>{0}</filename>のファイルタイプは開けません"

msgctxt "@item:inmenu"
msgid "Check for Updates"
msgstr "アップデートを確認"

msgctxt "description"
msgid "Checks for updates of the software."
msgstr "ソフトウェアのアップデートを確認する。"

msgctxt "@option"
msgid "Close"
msgstr "閉じる"

msgctxt "@info:title"
msgid "Configuration errors"
msgstr "構成エラー"

msgctxt "@title:window"
msgid "Confirm Remove"
msgstr "モデルを削除しました"

msgctxt "name"
msgid "Console Logger"
msgstr "コンソールロガー"

msgctxt "@info:status Don't translate the XML tags <filename> or <message>!"
msgid "Could not save to <filename>{0}</filename>: <message>{1}</message>"
msgstr "<filename>{0}</filename>を保存できませんでした: <message>{1}</message>"

msgctxt "Critical OpenGL Extensions Missing"
msgid "Critical OpenGL extensions are missing. This program requires support for Framebuffer Objects. Please check your video card drivers."
msgstr "重要なOpenGLの拡張が見つかりません。このプログラムではフレームバッファーオブジェクトのサポートが必須です。ビデオカードドライバーをチェックしてください。"

msgctxt "@option"
msgid "Discard"
msgstr "取り消す"

msgctxt "@action:button"
msgid "Dismiss"
msgstr "無視"

msgctxt "@action:button"
msgid "Download"
msgstr "ダウンロード"

msgctxt "@option:check"
msgid "Drop Down Model"
msgstr "モデルを下ろす"

msgctxt "description"
msgid "Enables saving to local files."
msgstr "ローカルファイルに保存することができます。"

msgctxt "@info:title"
msgid "Error"
msgstr "エラー"

msgctxt "@message"
msgid "Failed to Initialize OpenGL"
msgstr "OpenGLの初期化に失敗しました"

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Failed to load <filename>{0}</filename>. The file could be corrupt or inaccessible."
msgstr "<filename>{0}</filename>の読み込みに失敗しました。ファイルが破損しているか、アクセスできない可能性があります。"

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Failed to load <filename>{0}</filename>. The file could be corrupt, inaccessible or it did not contain any models."
msgstr "<filename>{0}</filename>の読み込みに失敗しました。ファイルが破損しているか、アクセスできないか、モデルが含まれていません。"

msgctxt "@title:window"
msgid "File Already Exists"
msgstr "すでに存在するファイルです"

msgctxt "name"
msgid "File Logger"
msgstr "ファイルロガー"

msgctxt "@info:title"
msgid "File Saved"
msgstr "ファイル保存完了"

msgctxt "@info:title"
msgid "File has been modified"
msgstr "ファイルが変更されました"

msgctxt "@menu"
msgid "From Disk"
msgstr "ディスクから"

msgctxt "@title:tab"
msgid "General"
msgstr "一般"

msgctxt "@option"
msgid "Help"
msgstr "ヘルプ"

msgctxt "@option"
msgid "Ignore"
msgstr "無視"

msgctxt "@info:progress"
msgid "Initializing package manager..."
msgstr "パッケージマネージャーを初期化中..."

msgctxt "@info:progress Don't translate {package_id}"
msgid "Installing plugin {package_id}..."
msgstr "プラグイン{package_id}をインストール中..."

msgctxt "@info:title"
msgid "Invalid File"
msgstr "無効なファイル"

msgctxt "@action:button"
msgid "Lay flat"
msgstr "平らに配置"

msgctxt "@label"
msgid "Laying object flat on buildplate..."
msgstr "オブジェクトをビルドプレート上に平に並べる..."

msgctxt "@action:button"
msgid "Learn more"
msgstr "詳しく見る"

msgctxt "@info:title"
msgid "Loading"
msgstr "読み込み中"

msgctxt "@info:progress"
msgid "Loading UI..."
msgstr "UIをロード中..."

msgctxt "@info:progress"
msgid "Loading plugin {plugin_id}..."
msgstr "プラグイン{plugin_id}をロード中..."

msgctxt "@info:progress"
msgid "Loading plugins..."
msgstr "プラグインをロード中..."

msgctxt "@info:progress"
msgid "Loading preferences..."
msgstr "環境設定をロード中..."

msgctxt "name"
msgid "Local Container Provider"
msgstr "ローカルコンテナプロバイダー"

msgctxt "@item:inmenu"
msgid "Local File"
msgstr "ローカルファイル"

msgctxt "name"
msgid "Local File Output Device"
msgstr "ローカルファイルアウトプットデバイス"

msgctxt "@option:check"
msgid "Lock Model"
msgstr "モデルをロック"

msgctxt "description"
msgid "Makes it possible to read Wavefront OBJ files."
msgstr "Wavefront OBJファイルの書き出しを可能にします。"

msgctxt "description"
msgid "Makes it possible to write Wavefront OBJ files."
msgstr "Wavefront OBJファイルの書き出しを可能にします。"

msgctxt "@label"
msgid "Mirror"
msgstr "反転"

msgctxt "@info:tooltip"
msgid "Mirror Model"
msgstr "モデルを反転させる"

msgctxt "name"
msgid "Mirror Tool"
msgstr "ミラーツール"

msgctxt "@action:button"
msgid "Move"
msgstr "移動"

msgctxt "@info:tooltip"
msgid "Move Model"
msgstr "モデルを移動する"

msgctxt "name"
msgid "Move Tool"
msgstr "移動ツール"

msgctxt "@option"
msgid "No"
msgstr "いいえ"

msgctxt "@info:title"
msgid "No Models in File"
msgstr "ファイルにモデルがありません"

msgctxt "@info"
msgid "No new version was found."
msgstr "新しいバージョンは見つかりませんでした。"

msgctxt "@option"
msgid "No to All"
msgstr "すべていいえ"

msgctxt "@error:not supported"
msgid "OBJWriter does not support non-text mode."
msgstr "OBJWriterは非テキストモードをサポートしていません。"

msgctxt "@action:button"
msgid "OK"
msgstr "OK"

msgctxt "@option"
msgid "OK"
msgstr "OK"

msgctxt "@title"
msgid "Object Rotation"
msgstr "オブジェクト回転"

msgctxt "@option"
msgid "Open"
msgstr "開く"

msgctxt "@action:button"
msgid "Open Folder"
msgstr "フォルダーを開く"

msgctxt "@info:tooltip"
msgid "Open the folder containing the file"
msgstr "ファイルが入ったフォルダーを開く"

msgctxt "description"
msgid "Outputs log information to a file in your settings folder."
msgstr "ログ情報を設定フォルダーのファイルに出力。"

msgctxt "description"
msgid "Outputs log information to the console."
msgstr "ログ情報をコンソールに出力。"

msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "Permission denied when trying to save <filename>{0}</filename>"
msgstr "<filename>{0}</filename>を保存する権限がありません"

msgctxt "@info"
msgid "Please provide a new name."
msgstr "新しい名前を入力してください。"

msgctxt "@info:status"
msgid "Plugin no longer scheduled to be installed."
msgstr "プラグインのインストールはスケジュールされなくなります。"

msgctxt "@error:untrusted"
msgid "Plugin {} was not loaded because it could not be verified."
msgstr "プラグイン{}は検証できなかったため、読み込まれていません。"

msgctxt "@title:window"
msgid "Preferences"
msgstr "環境設定"

msgctxt "@title:tab"
msgid "Printers"
msgstr "プリンター"

msgctxt "description"
msgid "Provides a simple solid mesh view."
msgstr "シンプルなソリッドメッシュビューを提供します。"

msgctxt "description"
msgid "Provides built-in setting containers that come with the installation of the application."
msgstr "アプリケーションのインストール時に導入されるビルトイン設定コンテナを提供します。"

msgctxt "description"
msgid "Provides support for reading STL files."
msgstr "STLファイル読み込み機能を提供します。"

msgctxt "description"
msgid "Provides support for writing STL files."
msgstr "STLファイル書き出し機能を提供します。"

msgctxt "description"
msgid "Provides the Mirror tool."
msgstr "反転ツールを提供します。"

msgctxt "description"
msgid "Provides the Move tool."
msgstr "移動ツールを提供します。"

msgctxt "description"
msgid "Provides the Rotate tool."
msgstr "回転ツールを提供します。"

msgctxt "description"
msgid "Provides the Scale tool."
msgstr "拡大／縮小ツールを提供します。"

msgctxt "description"
msgid "Provides the Selection tool."
msgstr "選択ツールを提供します。"

msgctxt "description"
msgid "Provides the tool to manipulate the camera."
msgstr "カメラ操作ツールを提供します。"

msgctxt "@action:button"
msgid "Reload"
msgstr "再読込"

msgctxt "@action:button"
msgid "Remove plugin"
msgstr "プラグインを削除"

msgctxt "@title:window"
msgid "Rename"
msgstr "名称変更"

msgctxt "@action:button"
msgid "Reset"
msgstr "リセット"

msgctxt "@option"
msgid "Reset"
msgstr "リセットする"

msgctxt "@title:window"
msgid "Reset to factory"
msgstr "ファクトリーリセット"

msgctxt "@label"
msgid "Reset will remove all your current printers and profiles! Are you sure you want to reset?"
msgstr "リセットすると、現在のプリンターおよびプロファイルがすべて削除されます。本当にリセットしてよろしいですか？"

msgctxt "@option"
msgid "Restore Defaults"
msgstr "デフォルトに戻す"

msgctxt "@option"
msgid "Retry"
msgstr "リトライ"

msgctxt "@label"
msgid "Rotate"
msgstr "回転"

msgctxt "@info:tooltip"
msgid "Rotate Model"
msgstr "モデルを回転させる"

msgctxt "name"
msgid "Rotate Tool"
msgstr "回転ツール"

msgctxt "@item:inlistbox"
msgid "STL File"
msgstr "STLファイル"

msgctxt "@item:inlistbox"
msgid "STL File (ASCII)"
msgstr "STLファイル (ASCII)"

msgctxt "@item:inlistbox"
msgid "STL File (Binary)"
msgstr "STLファイル（バイナリー）"

msgctxt "name"
msgid "STL Reader"
msgstr "STLリーダー"

msgctxt "name"
msgid "STL Writer"
msgstr "STLライター"

msgctxt "@option"
msgid "Save"
msgstr "保存"

msgctxt "@option"
msgid "Save All"
msgstr "すべて保存"

msgctxt "@action:button Preceded by 'Ready to'."
msgid "Save to Disk"
msgstr "ディスクに保存する"

msgctxt "@info:tooltip"
msgid "Save to Disk"
msgstr "ディスクに保存する"

msgctxt "@title:window"
msgid "Save to Disk"
msgstr "ディスクに保存する"

msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "Saved to <filename>{0}</filename>"
msgstr "<filename>{0}</filename>に保存しました"

msgctxt "@info:title"
msgid "Saving"
msgstr "保存中"

msgctxt "@info:progress Don't translate the XML tags <filename>!"
msgid "Saving to <filename>{0}</filename>"
msgstr "<filename>{0}</filename>に保存する"

msgctxt "@label"
msgid "Scale"
msgstr "拡大／縮小"

msgctxt "@info:tooltip"
msgid "Scale Model"
msgstr "モデルを拡大／縮小する"

msgctxt "name"
msgid "Scale Tool"
msgstr "拡大／縮小ツール"

msgctxt "@info:title"
msgid "Scaling Object"
msgstr "造形物を拡大／縮小する"

msgctxt "@action:button"
msgid "Select face to align to the build plate"
msgstr "ビルドプレートに合わせる面を選択します"

msgctxt "name"
msgid "Selection Tool"
msgstr "選択ツール"

msgctxt "@title:tab"
msgid "Settings"
msgstr "設定"

msgctxt "@item:inmenu"
msgid "Simple"
msgstr "シンプル"

msgctxt "name"
msgid "Simple View"
msgstr "シンプルビュー"

msgctxt "@action:checkbox"
msgid "Snap Rotation"
msgstr "段階的に回転"

msgctxt "@option:check"
msgid "Snap Scaling"
msgstr "段階的に拡大／縮小"

msgctxt "@label Don't translate the XML tag <filename>!"
msgid "The file <filename>{0}</filename> already exists. Are you sure you want to overwrite it?"
msgstr "<filename>{0}</filename> は既に存在します。ファイルを上書きしますか？"

msgctxt "@info:status"
msgid ""
"The plugin has been installed.\n"
"Please re-start the application to activate the plugin."
msgstr ""
"プラグインがインストールされました。\n"
"アプリケーションを再起動して、プラグインをアクティベートしてください。"

msgctxt "@info:status"
msgid ""
"The plugin has been removed.\n"
"Please restart {0} to finish uninstall."
msgstr ""
"プラグインが削除されました。\n"
"{0} を再起動してアンインストールを終了してください。"

msgctxt "@error"
msgid "The plugin {} could not be loaded. Re-installing the plugin might solve the issue."
msgstr "プラグイン{}を読み込めませんでした。プラグインを再インストールすると、問題が解決する場合があります。"

msgctxt "@info"
msgid "The version you are using does not support checking for updates."
msgstr "ご使用のバージョンはアップデート更新チェックをサポートしていません。"

msgctxt "@info:warning"
msgid "There are no file types available to write with!"
msgstr "書き出すために利用可能なファイルがありません！"

msgctxt "@error:no mesh"
msgid "There is no mesh to write."
msgstr "書き込むメッシュがありません。"

msgctxt "@error:update"
msgid ""
"There was an error uninstalling the package {package} before installing new version:\n"
"{error}.\n"
"Please try to upgrade again later."
msgstr ""
"新しいバージョンをインストールする前に、パッケージ {package} のアンインストールでエラーが発生しました:\n"
"{error}\n"
"後ほどアップグレードをやり直してください。"

msgctxt "@error:uninstall"
msgid "There were some errors uninstalling the following packages: {packages}"
msgstr "次のパッケージのアンインストールでエラーが発生しました:{packages}"

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "There where no models in <filename>{0}</filename>."
msgstr "<filename>{0}</filename>にはモデルがありませんでした。"

msgctxt "@action:description"
msgid "This will trigger the modified files to reload from disk."
msgstr "これにより変更したファイルをディスクから再ロードします。"

msgctxt "@item:inmenu About saving files to the hard drive"
msgid "To Disk"
msgstr "ディスクへ"

msgctxt "@info:status"
msgid "Try out the latest BETA version and help us improve {application_name}."
msgstr "最新のベータ版をお試しいただき、{application_name}の改善にご協力ください。"

msgctxt "@label"
msgid "Type"
msgstr "タイプ"

msgctxt "@info:title"
msgid "Unable to Open File"
msgstr "ファイルを開けません"

msgctxt "@option:check"
msgid "Uniform Scaling"
msgstr "縦・横・高さの比を維持"

msgctxt "@info:title"
msgid "Uninstalling errors"
msgstr "アンインストールエラー"

msgctxt "@error:not supported"
msgid "Unsupported output mode writing STL to stream."
msgstr "サポートされていない出力モードでSTLをストリームに書き込んでいます。"

msgctxt "@item:inmenu"
msgid "Update Checker"
msgstr "アップデートチェッカー"

msgctxt "name"
msgid "Update Checker"
msgstr "アップデートチェッカー"

msgctxt "@info:progress"
msgid "Updating configuration..."
msgstr "コンフィグレーションをアップデート中..."

msgctxt "@info:title"
msgid "Updating error"
msgstr "アップデートエラー"

msgctxt "@info:title"
msgid "Version Upgrade"
msgstr "バージョンアップグレード"

msgctxt "@info:title"
msgid "Warning"
msgstr "警告"

msgctxt "@item:inlistbox"
msgid "Wavefront OBJ File"
msgstr "Wavefront OBJファイル"

msgctxt "name"
msgid "Wavefront OBJ Reader"
msgstr "Wavefront OBJリーダー"

msgctxt "name"
msgid "Wavefront OBJ Writer"
msgstr "Wavefront OBJライター"

msgctxt "@info"
msgid "Would you like to reload {filename}?"
msgstr "{filename}を再ロードしますか？"

msgctxt "@option"
msgid "Yes"
msgstr "はい"

msgctxt "@option"
msgid "Yes to All"
msgstr "すべてはい"

msgctxt "@info:status"
msgid "Your configuration seems to be corrupt."
msgstr "構成が破損している可能性があります。"

msgctxt "@info:status"
msgid ""
"Your configuration seems to be corrupt. Something seems to be wrong with the following profiles:\n"
"- {profiles}\n"
" Would you like to reset to factory defaults? Reset will remove all your current printers and profiles."
msgstr ""
"構成が破損している可能性があります。以下のプロファイルでエラーが発生しています：\n"
"- {profiles}\n"
"工場出荷状態にリセットしますか？リセットすると使用してるすべてのプリンターおよびプロファイルが削除されます。"

msgctxt "@label Short days-hours-minutes format. {0} is days, {1} is hours, {2} is minutes"
msgid "{0:0>2}d {1:0>2}h {2:0>2}min"
msgstr "{0:0>2}日 {1:0>2}時間 {2:0>2}分"

msgctxt "@label Short hours-minutes format. {0} is hours, {1} is minutes"
msgid "{0:0>2}h {1:0>2}min"
msgstr "{0:0>2}時間 {1:0>2}分"

msgctxt "@label Long duration format. {0} is days"
msgid "{0} day"
msgid_plural "{0} days"
msgstr[0] "{0}日"

msgctxt "@label Long duration format. {0} is hours"
msgid "{0} hour"
msgid_plural "{0} hours"
msgstr[0] "{0}時間"

msgctxt "@label Long duration format. {0} is minutes"
msgid "{0} minute"
msgid_plural "{0} minutes"
msgstr[0] "{0}分"

msgctxt "@info:status"
msgid "{application_name} {version_number} is available!"
msgstr "{application_name} {version_number} が利用可能です！"

msgctxt "@info:status"
msgid "{application_name} {version_number} provides a better and more reliable printing experience."
msgstr "{application_name} {version_number} なら、より優れた信頼性の高い印刷体験が得られます。"

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "There were no models in <filename>{0}</filename>."
msgstr "<filename>{0}</filename>にはモデルがありませんでした。"
