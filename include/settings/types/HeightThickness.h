// Copyright (c) 2018 Ultimaker B.V.
// CuraEngine is released under the terms of the AGPLv3 or higher.

#ifndef HEIGHT_THICKNESS_H
#define HEIGHT_THICKNESS_H

namespace cura
{

/*
 * \brief Represents a HeightThickness in degrees Celsius.
 *
 * This is a facade. It behaves like a double.
 */
struct HeightThickness
{
    /*
     * \brief Default constructor setting the HeightThickness to 0.
     */
    HeightThickness()
        : value(0.0){};

    /*
     * \brief Casts a double to a HeightThickness instance.
     */
    HeightThickness(double value_t)
        : value(value_t){};

    /*
     * \brief Casts the HeightThickness instance to a double.
     */
    operator double() const
    {
        return value;
    }

    /*
     * Some operators to do arithmetic with HeightThickness.
     */
    HeightThickness operator+(const HeightThickness& other) const
    {
        return HeightThickness(value + other.value);
    };
    HeightThickness operator-(const HeightThickness& other) const
    {
        return HeightThickness(value - other.value);
    };
    HeightThickness& operator+=(const HeightThickness& other)
    {
        value += other.value;
        return *this;
    }
    HeightThickness& operator-=(const HeightThickness& other)
    {
        value -= other.value;
        return *this;
    }

    /*
     * \brief The actual HeightThickness, as a double.
     */
    double value = 0;
};

} // namespace cura

#endif // HEIGTHTHICKNESS_H
