// Copyright (c) 2020 Ultimaker B.V.
// CuraEngine is released under the terms of the AGPLv3 or higher.

#ifndef HEIGHT_THICKNESS_GRAPH
#define HEIGHT_THICKNESS_GRAPH

#include <cassert>
#include <vector>

#include "types/HeightThickness.h"

namespace cura
{

/*!
 * Class representing a graph matching a flow to a HeightThickness.
 * The graph generally consists of several linear line segments between points at which the temperature and flow are matched.
 */
class HeightThicknessGraph
{
public:
    struct Datum
    {
        double height_; //!< The flow in mm^3/s
        double thickness_; //!< The HeightThickness in *C
        Datum(double height, double thickness)
            : height_(height)
            , thickness_(thickness)
        {
        }
    };

    std::vector<Datum> data_; //!< The points of the graph between which the graph is linearly interpolated

    /*!
     * Get the HeightThickness corresponding to a specific flow.
     *
     * For flows outside of the chart, the HeightThickness at the minimal or maximal flow is returned.
     * When the graph is empty, the @p HeightThickness is returned.
     *
     * \param flow the flow in mm^3/s
     * \param material_print_temperature The default printing temp (backward compatibility for when the graph fails)
     * \return the corresponding temp
     */
    double getThickness(const double height, const double layer0_thickness, const double layerX_thickness, const double maxMeshZ) const;
};

} // namespace cura

#endif // HEIGHT_THICKNESS_GRAPH
